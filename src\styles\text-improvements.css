/* تحسينات الخطوط والنصوص */

/* تحسين لون النص في light mode */
.light-text-black {
  color: #000000 !important;
}

/* تحسين النصوص في الـ textarea و input في light mode */
textarea:not(.dark textarea),
input[type="text"]:not(.dark input[type="text"]) {
  color: #000000 !important;
  background-color: #ffffff !important;
}

/* تحسين النصوص في الـ dark mode */
.dark textarea,
.dark input[type="text"] {
  color: #ffffff !important;
  background-color: #374151 !important;
}

/* تحسين placeholder في light mode */
textarea::placeholder:not(.dark textarea::placeholder),
input[type="text"]::placeholder:not(.dark input[type="text"]::placeholder) {
  color: #6b7280 !important;
}

/* تحسين placeholder في dark mode */
.dark textarea::placeholder,
.dark input[type="text"]::placeholder {
  color: #9ca3af !important;
}

/* تحسين النصوص العربية */
.font-arabic {
  font-family: 'Tajawal', 'Cairo', '<PERSON><PERSON>', 'Noto Sans Arabic', sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* تحسين النصوص في العناوين */
h1, h2, h3, h4, h5, h6 {
  color: #111827;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #f9fafb;
}

/* تحسين النصوص في الفقرات */
p {
  color: #374151;
}

.dark p {
  color: #d1d5db;
}

/* تحسين النصوص في الأزرار */
button {
  font-weight: 500;
}

/* تحسين النصوص في الـ labels */
label {
  color: #374151;
  font-weight: 500;
}

.dark label {
  color: #d1d5db;
}

/* تحسين النصوص في الـ spans */
span {
  color: inherit;
}

/* تحسين النصوص في الـ divs */
div {
  color: inherit;
}

/* تحسين التباين في light mode */
@media (prefers-color-scheme: light) {
  * {
    --text-primary: #000000;
    --text-secondary: #374151;
    --text-muted: #6b7280;
  }
}

/* تحسين التباين في dark mode */
@media (prefers-color-scheme: dark) {
  * {
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
  }
}

/* تطبيق المتغيرات */
.text-primary {
  color: var(--text-primary) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

/* تحسينات RTL محسنة وشاملة */
[dir="rtl"] {
  text-align: right;
  direction: rtl;
}

/* ضمان أن جميع النصوص تبدأ من اليمين للعربية */
[dir="rtl"] * {
  text-align: right;
  direction: rtl;
}

/* تأكيد أن جميع العناصر تبدأ من أقصى اليمين */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
  text-align: right;
}

[dir="rtl"] p, [dir="rtl"] div, [dir="rtl"] span {
  text-align: right;
}

[dir="rtl"] button {
  text-align: right;
}

[dir="rtl"] input, [dir="rtl"] textarea {
  text-align: right;
  direction: rtl;
}

/* الأزرار والعناصر التفاعلية تبدأ من اليمين */
[dir="rtl"] .flex {
  justify-content: flex-end;
}

[dir="rtl"] .flex-row-reverse {
  justify-content: flex-end;
}

/* استثناءات للعناصر التي يجب أن تبقى في المنتصف */
[dir="rtl"] .text-center,
[dir="rtl"] .justify-center,
[dir="rtl"] .items-center {
  text-align: center;
  justify-content: center;
}

/* استثناءات للكود والأرقام */
[dir="rtl"] code,
[dir="rtl"] pre,
[dir="rtl"] .font-mono,
[dir="rtl"] .text-left-force {
  text-align: left;
  direction: ltr;
}

/* تأكيد محاذاة النص لليمين */
[dir="rtl"] .text-right {
  text-align: right !important;
  direction: rtl !important;
}

/* تأكيد أن العناصر ذات الكلاس text-right تبدأ من اليمين */
.text-right {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: right !important;
  direction: rtl !important;
}

/* إجبار جميع العناصر في RTL على البدء من اليمين */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
  text-align: right !important;
  direction: rtl !important;
}

[dir="rtl"] p, [dir="rtl"] div, [dir="rtl"] span, [dir="rtl"] button {
  text-align: right !important;
  direction: rtl !important;
}

/* إجبار الأزرار على البدء من اليمين */
[dir="rtl"] .flex {
  justify-content: flex-end !important;
  direction: rtl !important;
}

[dir="rtl"] .flex-row-reverse {
  justify-content: flex-end !important;
  direction: rtl !important;
}

/* إجبار جميع النصوص والعناصر على البدء من اليمين */
[dir="rtl"] * {
  text-align: right !important;
  direction: rtl !important;
}

/* استثناءات للعناصر المركزة */
[dir="rtl"] .text-center,
[dir="rtl"] .justify-center,
[dir="rtl"] .items-center,
[dir="rtl"] .mx-auto {
  text-align: center !important;
  justify-content: center !important;
}

/* استثناءات للكود */
[dir="rtl"] code,
[dir="rtl"] pre,
[dir="rtl"] .font-mono {
  text-align: left !important;
  direction: ltr !important;
}

/* تحسين الـ input fields للعربية */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
  direction: rtl;
  font-family: 'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
  line-height: 1.8;
  letter-spacing: 0.02em;
}

/* تحسين placeholder للعربية */
[dir="rtl"] input::placeholder,
[dir="rtl"] textarea::placeholder {
  text-align: right;
  direction: rtl;
  font-family: 'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
  opacity: 0.7;
}

/* تحسين الـ flex للعربية */
[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .space-x-1 > * + * {
  margin-left: 0;
  margin-right: 0.25rem;
}

[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .space-x-6 > * + * {
  margin-left: 0;
  margin-right: 1.5rem;
}

/* تحسين الـ grid للعربية */
[dir="rtl"] .grid {
  direction: rtl;
}

/* تحسين الـ buttons للعربية */
[dir="rtl"] .btn-group {
  flex-direction: row-reverse;
}

/* تحسين الـ navigation للعربية */
[dir="rtl"] .nav-item {
  margin-left: 0;
  margin-right: 1rem;
}

/* تحسين الـ cards للعربية */
[dir="rtl"] .card {
  text-align: right;
}

/* تحسين الـ modals للعربية */
[dir="rtl"] .modal {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

/* تحسين النصوص العربية */
[dir="rtl"] .font-arabic {
  font-family: 'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
  font-weight: 400;
  line-height: 1.8;
  letter-spacing: 0.02em;
  text-align: right;
  direction: rtl;
}

/* تحسين عرض النصوص في الـ pre و code */
[dir="rtl"] pre {
  text-align: left; /* الكود يبقى من اليسار */
  direction: ltr;
  font-family: 'Fira Code', 'Courier New', monospace;
}

[dir="rtl"] code {
  direction: ltr; /* الكود المضمن يبقى من اليسار */
  font-family: 'Fira Code', 'Courier New', monospace;
}

/* تحسين الـ labels */
[dir="rtl"] label {
  text-align: right;
  direction: rtl;
}

/* تحسين الـ paragraphs */
[dir="rtl"] p {
  text-align: right;
  direction: rtl;
  line-height: 1.8;
}

/* تحسين الـ headings */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
  text-align: right;
  direction: rtl;
}

/* تحسين الـ lists للعربية */
[dir="rtl"] ul,
[dir="rtl"] ol {
  text-align: right;
  direction: rtl;
  padding-right: 1.5rem;
  padding-left: 0;
}

[dir="rtl"] li {
  text-align: right;
  direction: rtl;
}

/* تحسين الـ tables للعربية */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}

/* تحسين الـ forms للعربية */
[dir="rtl"] .form-group {
  text-align: right;
}

[dir="rtl"] .form-label {
  text-align: right;
  display: block;
  margin-bottom: 0.5rem;
}

/* تحسين الـ tooltips للعربية */
[dir="rtl"] .tooltip {
  direction: rtl;
  text-align: right;
}

/* تحسين الـ dropdowns للعربية */
[dir="rtl"] .dropdown-menu {
  direction: rtl;
  text-align: right;
  right: 0;
  left: auto;
}

/* تحسين الـ breadcrumbs للعربية */
[dir="rtl"] .breadcrumb {
  direction: rtl;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  content: "\\";
  margin-left: 0;
  margin-right: 0.5rem;
}

/* تحسين الأيقونات والرموز للعربية */
[dir="rtl"] .icon-left {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .icon-right {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* تحسين الأزرار للعربية */
[dir="rtl"] .btn-with-icon {
  flex-direction: row-reverse;
}

/* تحسينات خاصة بصفحة إعدادات LLM API */
[dir="rtl"] .settings-page {
  direction: rtl;
}

[dir="rtl"] .provider-card {
  text-align: right;
}

[dir="rtl"] .provider-header {
  flex-direction: row-reverse;
}

[dir="rtl"] .provider-controls {
  flex-direction: row-reverse;
}

[dir="rtl"] .form-grid {
  direction: rtl;
}

[dir="rtl"] .form-label {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .form-input {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .model-checkbox {
  flex-direction: row-reverse;
}

[dir="rtl"] .model-info {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .button-group {
  flex-direction: row-reverse;
}

[dir="rtl"] .modal-content {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .error-message {
  flex-direction: row-reverse;
  text-align: right;
}

/* إصلاح مشاكل التخطيط المحددة */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse;
}

/* تحسين الأيقونات في RTL */
[dir="rtl"] .icon-before {
  margin-left: 0.5rem;
  margin-right: 0;
}

[dir="rtl"] .icon-after {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* تحسينات إضافية للتباعد في صفحة الإعدادات */
[dir="rtl"] .rtl-grid {
  direction: rtl;
}

[dir="rtl"] .rtl-grid > div {
  text-align: right;
}

/* إصلاح justify-between في RTL */
[dir="rtl"] .flex.justify-between {
  justify-content: space-between;
}

[dir="rtl"] .flex.justify-between.flex-row-reverse {
  justify-content: space-between;
  flex-direction: row-reverse;
}

/* تحسين المسافات بين العناصر */
[dir="rtl"] .provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

[dir="rtl"] .provider-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-direction: row-reverse;
}

[dir="rtl"] .provider-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-direction: row-reverse;
}

/* تحسين الأزرار في RTL */
[dir="rtl"] .btn-rtl {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-direction: row-reverse;
}

/* تحسين النماذج في RTL */
[dir="rtl"] .form-section {
  display: grid;
  gap: 1.5rem;
}

[dir="rtl"] .form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* تحسين التباعد العام */
[dir="rtl"] .space-y-4 > * + * {
  margin-top: 1rem;
}

[dir="rtl"] .space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* إصلاح مشاكل التداخل */
[dir="rtl"] .no-overlap {
  clear: both;
  display: block;
  width: 100%;
}

/* تحسين المحاذاة */
[dir="rtl"] .align-right {
  text-align: right !important;
  direction: rtl !important;
}

[dir="rtl"] .align-left {
  text-align: left !important;
  direction: ltr !important;
}

/* تحسينات إضافية لمنع التداخل */
[dir="rtl"] .provider-card {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
}

[dir="rtl"] .provider-card .provider-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 1rem;
}

[dir="rtl"] .provider-card .form-section {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

[dir="rtl"] .provider-card .button-section {
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* إصلاح مشاكل المسافات في الأزرار */
[dir="rtl"] .btn-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-direction: row-reverse;
}

[dir="rtl"] .btn-group button {
  white-space: nowrap;
}

/* تحسين التباعد في النماذج */
[dir="rtl"] .form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin: 1rem 0;
}

@media (max-width: 768px) {
  [dir="rtl"] .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* تحسين المحاذاة للعناصر المرنة */
[dir="rtl"] .flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 1rem;
}

[dir="rtl"] .flex-container.rtl {
  flex-direction: row-reverse;
}

/* إصلاح مشاكل النصوص الطويلة */
[dir="rtl"] .text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

[dir="rtl"] .text-wrap {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

[dir="rtl"] .btn-with-icon .icon {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* تحسين الـ alerts والـ notifications للعربية */
[dir="rtl"] .alert {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .alert .icon {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* تحسين الـ badges والـ tags للعربية */
[dir="rtl"] .badge {
  direction: rtl;
}

/* تحسين الـ progress bars للعربية */
[dir="rtl"] .progress {
  direction: rtl;
}

/* تحسين الـ tabs للعربية */
[dir="rtl"] .tab-list {
  direction: rtl;
}

[dir="rtl"] .tab-item {
  margin-left: 0;
  margin-right: 1rem;
}

/* تحسين الـ pagination للعربية */
[dir="rtl"] .pagination {
  direction: rtl;
  flex-direction: row-reverse;
}

/* تحسين الـ search bars للعربية */
[dir="rtl"] .search-input {
  text-align: right;
  direction: rtl;
  padding-right: 2.5rem;
  padding-left: 1rem;
}

[dir="rtl"] .search-icon {
  right: 0.75rem;
  left: auto;
}

/* تحسين الـ sidebars للعربية */
[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

[dir="rtl"] .sidebar-item {
  text-align: right;
  direction: rtl;
}

/* تحسين الـ menus للعربية */
[dir="rtl"] .menu {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .menu-item {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] .menu-item .icon {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* تحسين الأزرار في RTL */
[dir="rtl"] button {
  text-align: center;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* تحسين الـ Grid في RTL */
[dir="rtl"] .grid {
  direction: rtl;
}

/* تحسين الـ Flexbox في RTL */
[dir="rtl"] .justify-between {
  flex-direction: row-reverse;
}

[dir="rtl"] .items-center {
  align-items: center;
}
